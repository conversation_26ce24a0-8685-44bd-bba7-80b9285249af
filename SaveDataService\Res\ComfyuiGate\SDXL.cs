﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SaveDataService;
using SaveDataService.Manage;

namespace ComfyuiGate
{
    /// <summary>
    /// SDXL - ComfyUI工作流调用类
    /// 基于文件: SDXL.json
    /// 自动生成时间: 2025-06-07 12:40:26
    /// 继承RESTfulAPIBase，自动提供RESTful API功能
    /// </summary>
    public class SDXL : RESTfulAPIBase
    {
        /// <summary>
        /// 工作流JSON定义（ComfyUI API格式）
        /// </summary>
        private const string WORKFLOW_JSON = @"{
  ""146"": {
    ""inputs"": {
      ""samples"": [
        ""282"",
        0
      ],
      ""vae"": [
        ""652"",
        2
      ]
    },
    ""class_type"": ""VAEDecode"",
    ""_meta"": {
      ""title"": ""VAE解码""
    }
  },
  ""161"": {
    ""inputs"": {
      ""images"": [
        ""146"",
        0
      ]
    },
    ""class_type"": ""PreviewImage"",
    ""_meta"": {
      ""title"": ""Initial Image Generation""
    }
  },
  ""282"": {
    ""inputs"": {
      ""seed"": 885716716976314,
      ""steps"": 28,
      ""cfg"": 5,
      ""sampler_name"": ""euler"",
      ""scheduler"": ""normal"",
      ""denoise"": 1,
      ""noise_mode"": ""GPU(=A1111)"",
      ""batch_seed_mode"": ""comfy"",
      ""variation_seed"": 0,
      ""variation_strength"": 0,
      ""variation_method"": ""linear"",
      ""internal_seed"": 0,
      ""model"": [
        ""665"",
        0
      ],
      ""positive"": [
        ""666"",
        0
      ],
      ""negative"": [
        ""667"",
        0
      ],
      ""latent_image"": [
        ""692"",
        0
      ]
    },
    ""class_type"": ""KSampler //Inspire"",
    ""_meta"": {
      ""title"": ""Initial Image Generation""
    }
  },
  ""604"": {
    ""inputs"": {
      ""vae_name"": ""sdxl_vae.safetensors""
    },
    ""class_type"": ""VAELoader"",
    ""_meta"": {
      ""title"": ""加载VAE""
    }
  },
  ""652"": {
    ""inputs"": {
      ""ckpt_name"": ""IL\\illustriousXL_MMMix__v11.0.safetensors""
    },
    ""class_type"": ""CheckpointLoaderSimple"",
    ""_meta"": {
      ""title"": ""Checkpoint加载器（简易）""
    }
  },
  ""664"": {
    ""inputs"": {
      ""token_normalization"": ""length+mean"",
      ""weight_interpretation"": ""A1111"",
      ""wildcard_text"": [
        ""672"",
        0
      ],
      ""populated_text"": [
        ""672"",
        0
      ],
      ""mode"": ""populate"",
      ""Select to add LoRA"": ""Select the LoRA to add to the text"",
      ""Select to add Wildcard"": ""Select the Wildcard to add to the text"",
      ""seed"": 727148487125816,
      ""speak_and_recognation"": {
        ""__value__"": [
          false,
          true
        ]
      },
      ""model"": [
        ""687"",
        0
      ],
      ""clip"": [
        ""673"",
        0
      ]
    },
    ""class_type"": ""WildcardEncode //Inspire"",
    ""_meta"": {
      ""title"": ""Wildcard Encode (Inspire)""
    }
  },
  ""665"": {
    ""inputs"": {
      ""token_normalization"": ""none"",
      ""weight_interpretation"": ""A1111"",
      ""wildcard_text"": [
        ""689"",
        0
      ],
      ""populated_text"": [
        ""689"",
        0
      ],
      ""mode"": ""populate"",
      ""Select to add LoRA"": ""Select the LoRA to add to the text"",
      ""Select to add Wildcard"": ""Select the Wildcard to add to the text"",
      ""seed"": 77982000462159,
      ""speak_and_recognation"": {
        ""__value__"": [
          false,
          true
        ]
      },
      ""model"": [
        ""664"",
        0
      ],
      ""clip"": [
        ""673"",
        0
      ]
    },
    ""class_type"": ""WildcardEncode //Inspire"",
    ""_meta"": {
      ""title"": ""Wildcard Encode (Inspire)""
    }
  },
  ""666"": {
    ""inputs"": {
      ""width"": 1024,
      ""height"": 1024,
      ""crop_w"": 0,
      ""crop_h"": 0,
      ""target_width"": [
        ""691"",
        0
      ],
      ""target_height"": [
        ""690"",
        0
      ],
      ""text_g"": [
        ""664"",
        3
      ],
      ""text_l"": [
        ""664"",
        3
      ],
      ""speak_and_recognation"": {
        ""__value__"": [
          false,
          true
        ]
      },
      ""clip"": [
        ""664"",
        1
      ]
    },
    ""class_type"": ""CLIPTextEncodeSDXL"",
    ""_meta"": {
      ""title"": ""CLIP文本编码SDXL""
    }
  },
  ""667"": {
    ""inputs"": {
      ""width"": 1024,
      ""height"": 1024,
      ""crop_w"": 0,
      ""crop_h"": 0,
      ""target_width"": [
        ""691"",
        0
      ],
      ""target_height"": [
        ""690"",
        0
      ],
      ""text_g"": [
        ""665"",
        3
      ],
      ""text_l"": [
        ""665"",
        3
      ],
      ""speak_and_recognation"": {
        ""__value__"": [
          false,
          true
        ]
      },
      ""clip"": [
        ""665"",
        1
      ]
    },
    ""class_type"": ""CLIPTextEncodeSDXL"",
    ""_meta"": {
      ""title"": ""CLIP文本编码SDXL""
    }
  },
  ""672"": {
    ""inputs"": {
      ""text"": ""HG2512,line art,HG2512,line art,masterpiece,facing the lens,best quality,very aesthetic,1girl,extremely delicate and beautiful,Noise and grain,huge filesize,amazing quality,32K UHD,foreshortening,cinematic_angle,original,looking_at_viewer,bird_print,hagoromo,bird girl,illustration,((watercolor)),chromatic_aberration,blonde hair,formal_dress,halter_dress,ballgown,chiffon_skirt,two-sided_fabric,skyblue,opal render,plunging_neckline,feather hair ornament,upper_body,greco-roman architectur,byzantine_fashion,elf,crop_top_overhang,armlet,abstract_background,green eyes,bracelet,circlet,facial_mark,medium hair,(forehead:1.2),hime cut,dropping,expressionless,bored,mature_female,"",
      ""speak_and_recognation"": {
        ""__value__"": [
          false,
          true
        ]
      }
    },
    ""class_type"": ""CR Text"",
    ""_meta"": {
      ""title"": ""input-text-XL正面词""
    }
  },
  ""673"": {
    ""inputs"": {
      ""stop_at_clip_layer"": -2,
      ""clip"": [
        ""652"",
        1
      ]
    },
    ""class_type"": ""CLIPSetLastLayer"",
    ""_meta"": {
      ""title"": ""设置CLIP最后一层""
    }
  },
  ""687"": {
    ""inputs"": {
      ""lora_name"": ""xl\\动漫厚涂光影  Anime Impasto lora -Animagine XL V3.1_v1.0.safetensors"",
      ""strength_model"": 0.7000000000000002,
      ""model"": [
        ""652"",
        0
      ]
    },
    ""class_type"": ""LoraLoaderModelOnly"",
    ""_meta"": {
      ""title"": ""LoRA加载器（仅模型）""
    }
  },
  ""689"": {
    ""inputs"": {
      ""text"": ""worst quality,low quality,logo,text,watermark,signature,bad_hands,bad_feet,bad anatomy,bad proportions,"",
      ""speak_and_recognation"": {
        ""__value__"": [
          false,
          true
        ]
      }
    },
    ""class_type"": ""CR Text"",
    ""_meta"": {
      ""title"": ""input-text-XL负面词""
    }
  },
  ""690"": {
    ""inputs"": {
      ""value"": 1280
    },
    ""class_type"": ""easy int"",
    ""_meta"": {
      ""title"": ""input-int-高""
    }
  },
  ""691"": {
    ""inputs"": {
      ""value"": 758
    },
    ""class_type"": ""easy int"",
    ""_meta"": {
      ""title"": ""input-int-宽""
    }
  },
  ""692"": {
    ""inputs"": {
      ""width"": [
        ""691"",
        0
      ],
      ""height"": [
        ""690"",
        0
      ],
      ""batch_size"": [
        ""693"",
        0
      ]
    },
    ""class_type"": ""EmptyLatentImage"",
    ""_meta"": {
      ""title"": ""空Latent图像""
    }
  },
  ""693"": {
    ""inputs"": {
      ""value"": 1
    },
    ""class_type"": ""easy int"",
    ""_meta"": {
      ""title"": ""input-int-图片数量""
    }
  }
}";

        /// <summary>
        /// 运行工作流
        /// </summary>
        /// <param name="text_text">input-text-XL正面词 - text</param>
        /// <param name="text_speak_and_recognation">input-text-XL正面词 - speak_and_recognation</param>
        /// <param name="text_text1">input-text-XL负面词 - text</param>
        /// <param name="text_speak_and_recognation1">input-text-XL负面词 - speak_and_recognation</param>
        /// <param name="int_value">input-int-高 - value</param>
        /// <param name="int_value1">input-int-宽 - value</param>
        /// <param name="int_value2">input-int-图片数量 - value</param>
        /// <returns>任务ID</returns>
        public static async Task<string> runWorkflow(string text_text = "HG2512,line art,HG2512,line art,masterpiece,facing the lens,best quality,very aesthetic,1girl,extremely delicate and beautiful,Noise and grain,huge filesize,amazing quality,32K UHD,foreshortening,cinematic_angle,original,looking_at_viewer,bird_print,hagoromo,bird girl,illustration,((watercolor)),chromatic_aberration,blonde hair,formal_dress,halter_dress,ballgown,chiffon_skirt,two-sided_fabric,skyblue,opal render,plunging_neckline,feather hair ornament,upper_body,greco-roman architectur,byzantine_fashion,elf,crop_top_overhang,armlet,abstract_background,green eyes,bracelet,circlet,facial_mark,medium hair,(forehead:1.2),hime cut,dropping,expressionless,bored,mature_female,", string text_speak_and_recognation = @"{
  ""__value__"": [
    false,
    true
  ]
}", string text_text1 = "worst quality,low quality,logo,text,watermark,signature,bad_hands,bad_feet,bad anatomy,bad proportions,", string text_speak_and_recognation1 = @"{
  ""__value__"": [
    false,
    true
  ]
}", int int_value = 1280, int int_value1 = 758, int int_value2 = 1)
        {
            try
            {
                // 解析工作流JSON（已经是ComfyUI API格式）
                var workflow = JsonConvert.DeserializeObject<JObject>(WORKFLOW_JSON);
                if (workflow == null)
                {
                    throw new Exception("无法解析工作流JSON");
                }

                // 更新输入参数（工作流已经是API格式）
                // 更新节点 672 的 text 参数
                if (workflow["672"]?["inputs"]?["text"] != null)
                {
                    workflow["672"]!["inputs"]!["text"] = JToken.FromObject(text_text);
                }

                // 更新节点 672 的 speak_and_recognation 参数
                if (workflow["672"]?["inputs"]?["speak_and_recognation"] != null)
                {
                    workflow["672"]!["inputs"]!["speak_and_recognation"] = JToken.FromObject(text_speak_and_recognation);
                }

                // 更新节点 689 的 text 参数
                if (workflow["689"]?["inputs"]?["text"] != null)
                {
                    workflow["689"]!["inputs"]!["text"] = JToken.FromObject(text_text1);
                }

                // 更新节点 689 的 speak_and_recognation 参数
                if (workflow["689"]?["inputs"]?["speak_and_recognation"] != null)
                {
                    workflow["689"]!["inputs"]!["speak_and_recognation"] = JToken.FromObject(text_speak_and_recognation1);
                }

                // 更新节点 690 的 value 参数
                if (workflow["690"]?["inputs"]?["value"] != null)
                {
                    workflow["690"]!["inputs"]!["value"] = JToken.FromObject(int_value);
                }

                // 更新节点 691 的 value 参数
                if (workflow["691"]?["inputs"]?["value"] != null)
                {
                    workflow["691"]!["inputs"]!["value"] = JToken.FromObject(int_value1);
                }

                // 更新节点 693 的 value 参数
                if (workflow["693"]?["inputs"]?["value"] != null)
                {
                    workflow["693"]!["inputs"]!["value"] = JToken.FromObject(int_value2);
                }

                // 提交工作流到ComfyUI（已经是API格式，无需转换）
                string workflowJson = JsonConvert.SerializeObject(workflow);
                var comfyUIManage = ComfyUIManage.Instance;

                // 首先将工作流保存到数据库（如果不存在）
                string workflowId = EnsureWorkflowExists(workflowJson, "SDXL");
                if (string.IsNullOrEmpty(workflowId))
                {
                    Console.WriteLine("无法保存工作流到数据库");
                    return "";
                }

                // 获取可用的服务器
                var onlineServers = comfyUIManage.GetOnlineServers();
                if (onlineServers.Count == 0)
                {
                    Console.WriteLine("❌ 没有在线的ComfyUI服务器可用");
                    return "";
                }

                // 使用第一个在线服务器
                var selectedServer = onlineServers[0];
                Console.WriteLine($"🚀 使用服务器: {selectedServer.serverName} ({selectedServer.serverUrl}:{selectedServer.port})");

                // 直接提交工作流到ComfyUI服务器执行（API格式）
                var result = await comfyUIManage.SubmitWorkflowToServerWithMonitoring(selectedServer.id, workflowJson);
                string taskId = result.taskId;
                string submitResult = result.result;

                if (string.IsNullOrEmpty(taskId))
                {
                    Console.WriteLine($"工作流提交失败: {submitResult}");
                    return "";
                }

                // 检查提交结果
                if (submitResult.Contains("prompt_id") || submitResult.Contains("成功"))
                {
                    Console.WriteLine($"✅ 工作流已成功提交到ComfyUI服务器，任务ID: {taskId}");
                    Console.WriteLine($"服务器响应: {submitResult}");
                    
                    // 等待一段时间后检查是否有生成的文件
                    await Task.Delay(5000); // 等待5秒
                    await CheckGeneratedFiles(taskId);
                    
                    return taskId;
                }
                else
                {
                    Console.WriteLine($"❌ 工作流提交失败: {submitResult}");
                    return "";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"运行工作流失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 确保工作流存在于数据库中
        /// </summary>
        /// <param name="workflowJson">工作流JSON</param>
        /// <param name="workflowName">工作流名称</param>
        /// <returns>工作流ID</returns>
        private static string EnsureWorkflowExists(string workflowJson, string workflowName)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;
                return comfyUIManage.AddWorkflow(workflowName, workflowJson, "generated", $"自动生成的工作流: {workflowName}", "system");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存工作流到数据库失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 检查任务生成的文件
        /// </summary>
        /// <param name="taskId">任务ID</param>
        private static async Task CheckGeneratedFiles(string taskId)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;
                var task = comfyUIManage.GetTaskById(taskId);
                if (task != null)
                {
                    // 检查任务文件
                    var filesJson = comfyUIManage.GetTaskFiles(taskId);
                    if (!string.IsNullOrEmpty(filesJson) && filesJson != "[]")
                    {
                        Console.WriteLine($"📁 任务 {taskId} 生成的文件: {filesJson}");
                        
                        // 解析文件信息并检查文件大小
                        var files = JsonConvert.DeserializeObject<List<dynamic>>(filesJson);
                        if (files != null)
                        {
                            foreach (var file in files)
                            {
                                string filePath = file.filePath?.ToString() ?? "";
                                string fileName = file.fileName?.ToString() ?? "";
                                long fileSize = file.fileSize ?? 0;
                                
                                if (!string.IsNullOrEmpty(filePath) && System.IO.File.Exists(filePath))
                                {
                                    var actualSize = new System.IO.FileInfo(filePath).Length;
                                    Console.WriteLine($"✅ 文件存在: {fileName} (大小: {actualSize} 字节)");
                                }
                                else
                                {
                                    Console.WriteLine($"❌ 文件不存在: {fileName} (路径: {filePath})");
                                }
                            }
                        }
                    }
                    else
                    {
                        Console.WriteLine($"⚠️ 任务 {taskId} 暂未生成文件，可能仍在处理中");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查生成文件时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取任务状态
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务状态信息</returns>
        public static string GetTaskStatus(string taskId)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;
                var task = comfyUIManage.GetTaskById(taskId);
                if (task != null)
                {
                    return JsonConvert.SerializeObject(task, Formatting.Indented);
                }
                return "任务不存在";
            }
            catch (Exception ex)
            {
                return $"获取任务状态失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 获取API描述信息
        /// </summary>
        /// <returns>API描述信息</returns>
        public static string GetApiDescription()
        {
            return "工作流: SDXL\n" +
                   "描述: SDXL工作流\n" +
                   "参数:\n" +
                   "  - text_text: text\n" +
                   "  - text_speak_and_recognation: speak_and_recognation\n" +
                   "  - text_text1: text\n" +
                   "  - text_speak_and_recognation1: speak_and_recognation\n" +
                   "  - int_value: value\n" +
                   "  - int_value1: value\n" +
                   "  - int_value2: value\n" +
                   "";
        }

    }
}
