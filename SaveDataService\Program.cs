using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using ExcelToData;
using k8s;
using k8s.Models;
using SaveDataService.Manage;
using TencentCloud.Tke.V20180525.Models;
using SaveDataService;

partial class  Program
{
    static async Task Main(string[] args)
    {
        try
        {
            Console.WriteLine("=== 启动 RESTful API 服务器 ===");
            Console.WriteLine("跳过数据库初始化，仅启动 HTTP 服务器...");

            // 只启动 HTTP 服务器，跳过数据库相关操作
            NewPostManager.init();


            //导出整个的excel实体类数据
           // ExcelDataManager.Instance.ExcuteExcelData();
            ExcelDataManager.Instance.CreateDatabaseTables();
            //await DeepseekManager.Instance.init();
           // await NewDeepSeekManager.Instance.init();





            Console.WriteLine("服务器启动成功！");
            Console.WriteLine("访问 http://127.0.0.1:7778/debug-api.html 进行测试");

            // 测试ComfyUI系统
            Console.WriteLine("\n=== 开始测试ComfyUI管理系统 ===");
            Console.WriteLine("选择测试类型:");
            Console.WriteLine("1. 简单测试 (默认)");
            Console.WriteLine("2. 详细日志演示 (真实ComfyUI)");
            Console.WriteLine("3. 模拟日志演示 (不需要ComfyUI)");
            Console.WriteLine("4. 快速详细测试 (模拟)");
            Console.WriteLine("5. 实体类合并测试 (新)");
            Console.WriteLine("6. JSON存储功能测试 (新)");
            Console.WriteLine("7. 数据库验证测试 (新)");
            Console.WriteLine("8. 端到端完整流程测试 (最新)");
            Console.WriteLine("9. 数据库直接验证 (调试)");
            Console.WriteLine("10. ORMTools CSV生成演示 (新)");
            Console.WriteLine("11. ORMTools 详细使用示例 (新)");
            Console.WriteLine("12. AddWorkflows 工作流导入测试 (新)");
            Console.WriteLine("13. 工作流类生成测试 (最新)");
            Console.WriteLine("14. 验证SimpleWorkflowGenerator重写逻辑 (验证)");
            Console.WriteLine("15. 测试新的SimpleWorkflowGenerator (最新)");
            Console.WriteLine("16. ComfyUI服务器重复检查测试 (新)");
            Console.WriteLine("17. ComfyUI服务器初始化测试 (Init方法)");
            Console.WriteLine("18. 重新生成真实的ComfyUI工作流类 (删除欺骗代码)");
            Console.WriteLine("19. 测试真实的ComfyUI工作流执行 (需要ComfyUI运行)");
            Console.WriteLine("20. 全面测试所有生成的ComfyUI工作流类 (综合测试)");
            Console.Write("请输入选择 (1-20): ");

            var choice = Console.ReadLine();

            try
            {
                switch (choice)
                {
                    case "2":
                        Console.WriteLine("启动详细日志演示...");
                        await ComfyUILogDemo.RunLogDemo();
                        break;
                    case "3":
                        Console.WriteLine("启动模拟日志演示...");
                        await ComfyUILogDemo.RunMockLogDemo();
                        break;
                    case "4":
                        Console.WriteLine("启动快速详细测试...");
                        await ComfyUIDetailedTest.RunQuickDetailedTest();
                        break;
                    case "5":
                        Console.WriteLine("启动实体类合并测试...");
                        SaveDataService.Test.ComfyUITest.TestComfyUIBasicFunctions();
                        break;
                    case "6":
                        Console.WriteLine("启动JSON存储功能测试...");
                        SaveDataService.Test.ComfyUITest.TestJsonStorage();
                        break;
                    case "7":
                        Console.WriteLine("启动数据库验证测试...");
                        await ComfyUIDatabaseVerificationTest.TestDatabaseVerification();
                        break;
                    case "8":
                        Console.WriteLine("启动端到端完整流程测试...");
                        await ComfyUIEndToEndTest.RunCompleteTest();
                        break;
                    case "9":
                        Console.WriteLine("启动数据库直接验证...");
                        DatabaseVerification.VerifyDatabase();
                        Console.WriteLine("\n是否要清空所有数据? (y/N): ");
                        var clearChoice = Console.ReadLine();
                        if (clearChoice?.ToLower() == "y")
                        {
                            DatabaseVerification.ClearAllTestData();
                            DatabaseVerification.VerifyDatabase();
                        }
                        Console.WriteLine("\n是否要添加测试数据? (y/N): ");
                        var addChoice = Console.ReadLine();
                        if (addChoice?.ToLower() == "y")
                        {
                            DatabaseVerification.AddTestData();
                            DatabaseVerification.VerifyDatabase();
                        }
                        break;
                    case "10":
                        Console.WriteLine("启动ORMTools CSV生成演示...");
                        ORMToolsDemo.RunDemo();
                        break;
                    case "11":
                        Console.WriteLine("启动ORMTools 详细使用示例...");
                        ORMToolsUsageExample.DemonstrateORMToolsUsage();
                        break;
                    case "12":
                        Console.WriteLine("启动AddWorkflows 工作流导入测试...");
                        ComfyUILogDemo.TestAddWorkflows();
                        break;
                    case "13":
                        Console.WriteLine("启动工作流类生成测试...");
                        await SimpleWorkflowGenerator.GenerateAllWorkflowsAsync();
                        break;
                    case "14":
                        Console.WriteLine("启动SimpleWorkflowGenerator重写逻辑验证...");
                        // await ValidateLogic.ValidateGenerationLogic();
                        Console.WriteLine("此功能暂未实现");
                        break;
                    case "15":
                        Console.WriteLine("启动新的SimpleWorkflowGenerator测试...");
                        await TestAddWorkflows.TestSimpleWorkflowGenerator();
                        break;
                    case "16":
                        Console.WriteLine("启动ComfyUI服务器重复检查测试...");
                        await ComfyUIServerDuplicateTest.RunDuplicateTests();
                        ComfyUIServerDuplicateTest.TestIsServerExists();
                        break;
                    case "17":
                        Console.WriteLine("启动ComfyUI服务器初始化测试...");
                        await ComfyUIServerDuplicateTest.TestInitMethod();
                        break;
                    case "18":
                        Console.WriteLine("重新生成真实的ComfyUI工作流类...");
                        await SimpleWorkflowGenerator.GenerateAllWorkflowsAsync();
                        break;
                    case "19":
                        Console.WriteLine("测试真实的ComfyUI工作流执行...");
                        await TestRealWorkflow.RunTest();
                        break;
                    case "20":
                        Console.WriteLine("全面测试所有生成的ComfyUI工作流类...");
                        await Test20AllComfyUIWorkflowsTest.RunAllWorkflowsTest();
                        break;
                    default:
                        Console.WriteLine("启动简单测试...");
                        await ComfyUISimpleTest.RunSimpleTests();
                        break;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ComfyUI测试失败: {ex.Message}");
            }

            // 保持程序运行
            Console.WriteLine("按任意键退出...");
            try
            {
                if (Console.IsInputRedirected)
                {
                    // 如果输入被重定向，等待一段时间后自动退出
                    Console.WriteLine("检测到输入重定向，3秒后自动退出...");
                    System.Threading.Thread.Sleep(3000);
                }
                else
                {
                    Console.ReadKey();
                }
            }
            catch (InvalidOperationException)
            {
                // 如果无法读取键盘输入，等待一段时间后退出
                Console.WriteLine("无法读取键盘输入，3秒后自动退出...");
                System.Threading.Thread.Sleep(3000);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"程序运行出错: {ex.Message}");
            Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            Console.WriteLine("按任意键退出...");
            try
            {
                if (Console.IsInputRedirected)
                {
                    Console.WriteLine("检测到输入重定向，3秒后自动退出...");
                    System.Threading.Thread.Sleep(3000);
                }
                else
                {
                    Console.ReadKey();
                }
            }
            catch (InvalidOperationException)
            {
                Console.WriteLine("无法读取键盘输入，3秒后自动退出...");
                System.Threading.Thread.Sleep(3000);
            }
        }


    }

    static async Task Main1(string[] args)
    {
        //查看腾讯云指定命名空间中，所有重启过的pod 的重启次数等信息
        //string kubeConfigPath = @"C:\Users\<USER>\.kube\config";
        string kubeConfigPath = Path.Combine(AppContext.BaseDirectory, "config");
        string[] targetNamespaces = {
            "hse-data-save-android-prod",
            "hse-data-save-ios-prod",
            "hsw-data-save-android-prod",
            "hsw-data-save-ios-prod",
            "hse-data-save-dev"
        };

        if (!File.Exists(kubeConfigPath))
        {
            Console.WriteLine("错误: kubeconfig 文件未找到！");
            return;
        }

        var config = KubernetesClientConfiguration.BuildConfigFromConfigFile(kubeConfigPath);
        var client = new Kubernetes(config);

        try
        {
            Console.WriteLine("开始检查指定命名空间的Pod重启情况...");

            // 遍历目标命名空间
            foreach (string namespaceName in targetNamespaces)
            {
                Console.WriteLine($"\n命名空间: {namespaceName}");

                try
                {
                    // 获取该命名空间下的所有Pod
                    var podList = await client.ListNamespacedPodAsync(namespaceName);

                    if (podList.Items.Count == 0)
                    {
                        Console.WriteLine("  无Pod");
                        continue;
                    }

                    // 遍历该命名空间下的所有Pod
                    foreach (var pod in podList.Items)
                    {

                        // 检查Pod是否有容器状态
                        if (pod.Status?.ContainerStatuses == null || !pod.Status.ContainerStatuses.Any())
                        {
                            Console.WriteLine("    无容器状态信息");
                            continue;
                        }

                        // 遍历Pod的所有容器，输出重启次数
                        foreach (var containerStatus in pod.Status.ContainerStatuses)
                        {
                            // 如果重启过，输出终止信息
                            if (containerStatus.RestartCount > 0)
                            {
                                Console.WriteLine($"  Pod名称: {pod.Metadata.Name}");
                                Console.WriteLine($"  Pod状态: {pod.Status.Phase}");
                                Console.WriteLine($"  Pod创建时间: {pod.Metadata.CreationTimestamp}");
                                Console.WriteLine($"    容器: {containerStatus.Name}");
                                Console.WriteLine($"      重启次数: {containerStatus.RestartCount}");
                                Console.WriteLine($"      就绪状态: {containerStatus.Ready}");
                                if (containerStatus.LastState?.Terminated != null)
                                {
                                    Console.WriteLine($"      上次终止时间: {containerStatus.LastState.Terminated.FinishedAt}");
                                    Console.WriteLine($"      终止原因: {containerStatus.LastState.Terminated.Reason}");
                                    Console.WriteLine($"      退出代码: {containerStatus.LastState.Terminated.ExitCode}");
                                }
                                if (containerStatus.State?.Waiting != null)
                                {
                                    Console.WriteLine($"      等待原因: {containerStatus.State.Waiting.Reason}");
                                }
                                Console.WriteLine($"  --------------Pod分割线-----------------");
                            }
                        }                     
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  查询命名空间 {namespaceName} 时出错: {ex.Message}");
                }
            }

            Console.WriteLine("\n检查完成！");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"全局错误: {ex.Message}");
        }
    }
}