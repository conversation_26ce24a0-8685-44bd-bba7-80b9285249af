using System;
using System.IO;
using System.Threading.Tasks;
using SaveDataService.Manage;
using ComfyuiGate;

namespace SaveDataService
{
    /// <summary>
    /// 测试真实的ComfyUI工作流执行
    /// 验证工作流类是否真正调用ComfyUI服务器
    /// </summary>
    public class TestRealWorkflow
    {
        /// <summary>
        /// 运行真实工作流测试
        /// </summary>
        public static async Task RunTest()
        {
            Console.WriteLine("=== 真实ComfyUI工作流测试 ===");
            Console.WriteLine("这个测试将验证工作流类是否真正调用ComfyUI服务器");

            try
            {
                var comfyUIManage = ComfyUIManage.Instance;

                // 1. 检查数据库中的服务器
                Console.WriteLine("\n1. 检查数据库中的ComfyUI服务器...");
                var allServers = comfyUIManage.GetAllServers();
                Console.WriteLine($"   数据库中共有 {allServers.Count} 台服务器");

                if (allServers.Count == 0)
                {
                    Console.WriteLine("❌ 数据库中没有ComfyUI服务器！");
                    Console.WriteLine("请先添加ComfyUI服务器到数据库");
                    return;
                }

                // 2. 测试服务器连接状态
                Console.WriteLine("\n2. 测试服务器连接状态...");
                var onlineCount = await comfyUIManage.TestAllServerConnections();
                Console.WriteLine($"   在线服务器数量: {onlineCount}");

                var onlineServers = comfyUIManage.GetOnlineServers();
                if (onlineServers.Count == 0)
                {
                    Console.WriteLine("❌ 没有在线的ComfyUI服务器！");
                    Console.WriteLine("请确保至少有一台ComfyUI服务器在运行");

                    Console.WriteLine("\n数据库中的服务器列表:");
                    foreach (var server in allServers)
                    {
                        Console.WriteLine($"   - {server.serverName}: {server.serverUrl}:{server.port} (状态: {(server.status == 1 ? "在线" : "离线")})");
                    }
                    return;
                }

                Console.WriteLine($"✅ 找到 {onlineServers.Count} 台在线服务器");
                foreach (var server in onlineServers)
                {
                    Console.WriteLine($"   - {server.serverName}: {server.serverUrl}:{server.port}");
                }

                // 3. 检查是否有生成的工作流类
                Console.WriteLine("\n3. 检查生成的工作流类...");
                string gateDirectory = Path.Combine(Directory.GetCurrentDirectory(), "Res", "ComfyuiGate");
                if (!Directory.Exists(gateDirectory))
                {
                    Console.WriteLine("❌ ComfyuiGate目录不存在，请先运行选项18生成工作流类");
                    return;
                }

                var csFiles = Directory.GetFiles(gateDirectory, "*.cs");
                if (csFiles.Length == 0)
                {
                    Console.WriteLine("❌ 没有找到生成的工作流类，请先运行选项18生成工作流类");
                    return;
                }

                Console.WriteLine($"✅ 找到 {csFiles.Length} 个工作流类文件");
                foreach (var file in csFiles)
                {
                    Console.WriteLine($"   - {Path.GetFileName(file)}");
                }

                // 4. 测试AceStepAll工作流 - 真正调用ComfyUI服务器
                Console.WriteLine("\n4. 测试AceStepAll音乐生成工作流...");
                Console.WriteLine("   🚀 调用 AceStepAll.runWorkflow() 方法 - 真正提交到ComfyUI服务器...");

                try
                {
                    var taskId = await AceStepAll.runWorkflow(
                        "[verse]\n真实测试调用 (real test!)\n验证服务器执行 (verify server execution!)",
                        20
                    );

                    if (string.IsNullOrEmpty(taskId))
                    {
                        Console.WriteLine("❌ 工作流执行失败，返回空任务ID");
                        return;
                    }

                    Console.WriteLine($"✅ 工作流执行成功，任务ID: {taskId}");

                    // 5. 监控任务状态
                    Console.WriteLine("\n5. 监控任务执行状态...");
                    for (int i = 0; i < 10; i++)
                    {
                        await Task.Delay(3000); // 等待3秒

                        var status = AceStepAll.GetTaskStatus(taskId);
                        Console.WriteLine($"   第{i + 1}次检查 - 任务状态: {status}");

                        // 检查是否包含真实的任务信息
                        if (status.Contains("prompt_id") || status.Contains("运行中") || status.Contains("已完成") || status.Contains("成功"))
                        {
                            Console.WriteLine("✅ 检测到真实的任务执行状态！");

                            // 检查生成的文件
                            Console.WriteLine("\n6. 检查生成的文件...");
                            var files = comfyUIManage.GetTaskFiles(taskId);
                            if (!string.IsNullOrEmpty(files) && files != "[]")
                            {
                                Console.WriteLine($"📁 生成的文件: {files}");
                            }
                            else
                            {
                                Console.WriteLine("⏳ 文件可能仍在生成中...");
                            }
                            break;
                        }

                        if (i == 9)
                        {
                            Console.WriteLine("⚠️ 任务状态检查超时，但任务可能仍在后台运行");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ 工作流调用失败: {ex.Message}");
                    Console.WriteLine($"详细错误: {ex.StackTrace}");
                }

                Console.WriteLine("\n=== 测试完成 ===");
                Console.WriteLine("如果看到任务ID和状态信息，说明工作流确实调用了ComfyUI服务器");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
            }
        }
    }
}
