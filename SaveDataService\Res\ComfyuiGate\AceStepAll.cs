﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SaveDataService;
using SaveDataService.Manage;

namespace ComfyuiGate
{
    /// <summary>
    /// AceStepAll - ComfyUI工作流调用类
    /// 基于文件: ace-step-all.json
    /// 自动生成时间: 2025-06-07 12:40:26
    /// 继承RESTfulAPIBase，自动提供RESTful API功能
    /// </summary>
    public class AceStepAll : RESTfulAPIBase
    {
        /// <summary>
        /// 工作流JSON定义（ComfyUI API格式）
        /// </summary>
        private const string WORKFLOW_JSON = @"{
  ""1"": {
    ""inputs"": {
      ""multi_line_prompt"": ""[verse]\n哎呀跳起来，脚尖踩节拍 (oo-yeah!)\n灯光闪烁像星星盛开 (uh-huh!)\n人人都醒来，把烦恼踹开 (get it!)\n热血沸腾，汗水自己安排\n\n[chorus]\n嘿，你还等啥？快抓住节拍 (come on!)\n光芒指引，让心都不存在 (whoa!)\n点燃热火，我们一起飙high (let’s go!)\n跳入午夜的狂欢时代\n\n[bridge]\n咚咚鼓声啊，让你的灵魂起飞 (woo!)\n手心拍一拍，能量翻倍 (ah-hah!)\n键盘响起来，如宇宙的交汇 (oh yeah!)\n就是这感觉，兄弟姐妹都陶醉\n\n[verse]\n灵魂从不睡，只想继续燃烧 (woo!)\n节奏像热浪，席卷这街道 (ow!)\n大伙儿涌上楼台，满面微笑 (yeah!)\n这一刻属于我们，无可替代\n\n[chorus]\n嘿，你还等啥？快抓住节拍 (come on!)\n光芒指引，让心都不存在 (whoa!)\n点燃热火，我们一起飙high (let’s go!)\n跳入午夜的狂欢时代\n\n[verse]\n世界多精彩，握紧把它打开 (alright!)\n每一步都像星球在摇摆 (uh-huh!)\n无边无际的律动像大海 (oo-yeah!)\n跟着光芒之舞，一起澎湃"",
      ""speak_and_recognation"": {
        ""__value__"": [
          false,
          true
        ]
      }
    },
    ""class_type"": ""MultiLineLyrics"",
    ""_meta"": {
      ""title"": ""input-musictxt-歌词""
    }
  },
  ""2"": {
    ""inputs"": {
      ""multi_line_prompt"": ""hip-house, funk"",
      ""speak_and_recognation"": {
        ""__value__"": [
          false,
          true
        ]
      }
    },
    ""class_type"": ""MultiLinePromptACES"",
    ""_meta"": {
      ""title"": ""ACE-Step Prompt""
    }
  },
  ""5"": {
    ""inputs"": {
      ""audio_duration"": 204.19997916666668,
      ""infer_step"": [
        ""32"",
        0
      ],
      ""guidance_scale"": 15,
      ""scheduler_type"": ""euler"",
      ""cfg_type"": ""apg"",
      ""omega_scale"": 10,
      ""seed"": [
        ""31"",
        0
      ],
      ""guidance_interval"": 0.5,
      ""guidance_interval_decay"": 0,
      ""min_guidance_scale"": 3,
      ""use_erg_tag"": true,
      ""use_erg_lyric"": true,
      ""use_erg_diffusion"": true,
      ""oss_steps"": """",
      ""guidance_scale_text"": 0,
      ""guidance_scale_lyric"": 0
    },
    ""class_type"": ""GenerationParameters"",
    ""_meta"": {
      ""title"": ""ACE-Step Parameters""
    }
  },
  ""6"": {
    ""inputs"": {
      ""format"": ""WAV"",
      ""filename_prefix"": ""1"",
      ""audio"": [
        ""8"",
        0
      ]
    },
    ""class_type"": ""SaveAudioMW"",
    ""_meta"": {
      ""title"": ""output-audio-ace""
    }
  },
  ""8"": {
    ""inputs"": {
      ""unload_model"": true,
      ""prompt"": [
        ""2"",
        0
      ],
      ""lyrics"": [
        ""1"",
        0
      ],
      ""parameters"": [
        ""5"",
        0
      ]
    },
    ""class_type"": ""ACEStepGen"",
    ""_meta"": {
      ""title"": ""ACE-Step""
    }
  },
  ""31"": {
    ""inputs"": {
      ""value"": 0
    },
    ""class_type"": ""PrimitiveInt"",
    ""_meta"": {
      ""title"": ""随机种子""
    }
  },
  ""32"": {
    ""inputs"": {
      ""value"": 60
    },
    ""class_type"": ""easy int"",
    ""_meta"": {
      ""title"": ""input-step-步数""
    }
  }
}";

        /// <summary>
        /// 运行工作流
        /// </summary>
        /// <param name="musictxt_multi_line_prompt">input-musictxt-歌词 - multi_line_prompt</param>
        /// <param name="musictxt_speak_and_recognation">input-musictxt-歌词 - speak_and_recognation</param>
        /// <param name="step_value">input-step-步数 - value</param>
        /// <returns>任务ID</returns>
        public static async Task<string> runWorkflow(string musictxt_multi_line_prompt = @"[verse]
哎呀跳起来，脚尖踩节拍 (oo-yeah!)
灯光闪烁像星星盛开 (uh-huh!)
人人都醒来，把烦恼踹开 (get it!)
热血沸腾，汗水自己安排

[chorus]
嘿，你还等啥？快抓住节拍 (come on!)
光芒指引，让心都不存在 (whoa!)
点燃热火，我们一起飙high (let’s go!)
跳入午夜的狂欢时代

[bridge]
咚咚鼓声啊，让你的灵魂起飞 (woo!)
手心拍一拍，能量翻倍 (ah-hah!)
键盘响起来，如宇宙的交汇 (oh yeah!)
就是这感觉，兄弟姐妹都陶醉

[verse]
灵魂从不睡，只想继续燃烧 (woo!)
节奏像热浪，席卷这街道 (ow!)
大伙儿涌上楼台，满面微笑 (yeah!)
这一刻属于我们，无可替代

[chorus]
嘿，你还等啥？快抓住节拍 (come on!)
光芒指引，让心都不存在 (whoa!)
点燃热火，我们一起飙high (let’s go!)
跳入午夜的狂欢时代

[verse]
世界多精彩，握紧把它打开 (alright!)
每一步都像星球在摇摆 (uh-huh!)
无边无际的律动像大海 (oo-yeah!)
跟着光芒之舞，一起澎湃", string musictxt_speak_and_recognation = @"{
  ""__value__"": [
    false,
    true
  ]
}", int step_value = 60)
        {
            try
            {
                // 解析工作流JSON（已经是ComfyUI API格式）
                var workflow = JsonConvert.DeserializeObject<JObject>(WORKFLOW_JSON);
                if (workflow == null)
                {
                    throw new Exception("无法解析工作流JSON");
                }

                // 更新输入参数（工作流已经是API格式）
                // 更新节点 1 的 multi_line_prompt 参数
                if (workflow["1"]?["inputs"]?["multi_line_prompt"] != null)
                {
                    workflow["1"]!["inputs"]!["multi_line_prompt"] = JToken.FromObject(musictxt_multi_line_prompt);
                }

                // 更新节点 1 的 speak_and_recognation 参数
                if (workflow["1"]?["inputs"]?["speak_and_recognation"] != null)
                {
                    workflow["1"]!["inputs"]!["speak_and_recognation"] = JToken.FromObject(musictxt_speak_and_recognation);
                }

                // 更新节点 32 的 value 参数
                if (workflow["32"]?["inputs"]?["value"] != null)
                {
                    workflow["32"]!["inputs"]!["value"] = JToken.FromObject(step_value);
                }

                // 提交工作流到ComfyUI（已经是API格式，无需转换）
                string workflowJson = JsonConvert.SerializeObject(workflow);
                var comfyUIManage = ComfyUIManage.Instance;

                // 首先将工作流保存到数据库（如果不存在）
                string workflowId = EnsureWorkflowExists(workflowJson, "AceStepAll");
                if (string.IsNullOrEmpty(workflowId))
                {
                    Console.WriteLine("无法保存工作流到数据库");
                    return "";
                }

                // 获取可用的服务器
                var onlineServers = comfyUIManage.GetOnlineServers();
                if (onlineServers.Count == 0)
                {
                    Console.WriteLine("❌ 没有在线的ComfyUI服务器可用");
                    return "";
                }

                // 使用第一个在线服务器
                var selectedServer = onlineServers[0];
                Console.WriteLine($"🚀 使用服务器: {selectedServer.serverName} ({selectedServer.serverUrl}:{selectedServer.port})");

                // 直接提交工作流到ComfyUI服务器执行（API格式）
                var result = await comfyUIManage.SubmitWorkflowToServerWithMonitoring(selectedServer.id, workflowJson);
                string taskId = result.taskId;
                string submitResult = result.result;

                if (string.IsNullOrEmpty(taskId))
                {
                    Console.WriteLine($"工作流提交失败: {submitResult}");
                    return "";
                }

                // 检查提交结果
                if (submitResult.Contains("prompt_id") || submitResult.Contains("成功"))
                {
                    Console.WriteLine($"✅ 工作流已成功提交到ComfyUI服务器，任务ID: {taskId}");
                    Console.WriteLine($"服务器响应: {submitResult}");
                    
                    // 等待一段时间后检查是否有生成的文件
                    await Task.Delay(5000); // 等待5秒
                    await CheckGeneratedFiles(taskId);
                    
                    return taskId;
                }
                else
                {
                    Console.WriteLine($"❌ 工作流提交失败: {submitResult}");
                    return "";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"运行工作流失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 确保工作流存在于数据库中
        /// </summary>
        /// <param name="workflowJson">工作流JSON</param>
        /// <param name="workflowName">工作流名称</param>
        /// <returns>工作流ID</returns>
        private static string EnsureWorkflowExists(string workflowJson, string workflowName)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;
                return comfyUIManage.AddWorkflow(workflowName, workflowJson, "generated", $"自动生成的工作流: {workflowName}", "system");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存工作流到数据库失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 检查任务生成的文件
        /// </summary>
        /// <param name="taskId">任务ID</param>
        private static async Task CheckGeneratedFiles(string taskId)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;
                var task = comfyUIManage.GetTaskById(taskId);
                if (task != null)
                {
                    // 检查任务文件
                    var filesJson = comfyUIManage.GetTaskFiles(taskId);
                    if (!string.IsNullOrEmpty(filesJson) && filesJson != "[]")
                    {
                        Console.WriteLine($"📁 任务 {taskId} 生成的文件: {filesJson}");
                        
                        // 解析文件信息并检查文件大小
                        var files = JsonConvert.DeserializeObject<List<dynamic>>(filesJson);
                        if (files != null)
                        {
                            foreach (var file in files)
                            {
                                string filePath = file.filePath?.ToString() ?? "";
                                string fileName = file.fileName?.ToString() ?? "";
                                long fileSize = file.fileSize ?? 0;
                                
                                if (!string.IsNullOrEmpty(filePath) && System.IO.File.Exists(filePath))
                                {
                                    var actualSize = new System.IO.FileInfo(filePath).Length;
                                    Console.WriteLine($"✅ 文件存在: {fileName} (大小: {actualSize} 字节)");
                                }
                                else
                                {
                                    Console.WriteLine($"❌ 文件不存在: {fileName} (路径: {filePath})");
                                }
                            }
                        }
                    }
                    else
                    {
                        Console.WriteLine($"⚠️ 任务 {taskId} 暂未生成文件，可能仍在处理中");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查生成文件时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取任务状态
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务状态信息</returns>
        public static string GetTaskStatus(string taskId)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;
                var task = comfyUIManage.GetTaskById(taskId);
                if (task != null)
                {
                    return JsonConvert.SerializeObject(task, Formatting.Indented);
                }
                return "任务不存在";
            }
            catch (Exception ex)
            {
                return $"获取任务状态失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 获取API描述信息
        /// </summary>
        /// <returns>API描述信息</returns>
        public static string GetApiDescription()
        {
            return "工作流: AceStepAll\n" +
                   "描述: AceStepAll工作流\n" +
                   "参数:\n" +
                   "  - musictxt_multi_line_prompt: multi_line_prompt\n" +
                   "  - musictxt_speak_and_recognation: speak_and_recognation\n" +
                   "  - step_value: value\n" +
                   "";
        }

    }
}
